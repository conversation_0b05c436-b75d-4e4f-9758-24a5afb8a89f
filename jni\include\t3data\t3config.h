// T3Config.h
#pragma once

#include <string>

namespace T3Config {
    // t3网络验证配置
    static const std::string Host = "http://w.t3yanzheng.com"; // t3网络验证线路地址
    static const std::string AppKey = "16e2efa7e08581f22816fb76a9e62a43"; // t3网络验证程序应用密钥
    static const std::string Base64key = "rF2k3X4OYNAJ/djutHWPa0I9gCshSolfi7G6xwezyUpDQ5Lcv8B+qKVnmZbRM1TE"; // t3网络验证程序Base64自定义编码集

    // API路径
    static const std::string Path_SingleLogin = "AB7E80BDF943E04B"; // t3网络验证程序单码卡密登录API调用路径
    static const std::string Path_IsSingleLoginStatus = "00DF033786BBEAA6"; // t3网络验证程序单码卡密登录状态查询API调用路径
    static const std::string Path_GetProgramNotice = "1ACF0E35DEF2493C"; // t3网络验证程序获取公告API调用路径
    static const std::string Path_GetProgramVersionNumber = "BEDDA05C0C8E2ACA"; // t3网络验证程序获取版本号API调用路径
    static const std::string Path_GetValueContent = "CE8D53FB5E0EE2FF"; // t3网络验证程序获取变量内容API调用路径
}